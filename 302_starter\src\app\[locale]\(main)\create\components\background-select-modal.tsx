import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface BackgroundSelectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BackgroundSelectModal({
  open,
  onOpenChange,
}: BackgroundSelectModalProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>更换背景</DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="system" className="">
            <TabsList>
              <TabsTrigger value="text">文字</TabsTrigger>
              <TabsTrigger value="image">图片</TabsTrigger>
            </TabsList>
            <TabsContent value="text">
              <div className="flex">
                <div>原图</div>
                <Textarea />
              </div>
            </TabsContent>
            <TabsContent value="image">
              <div className="flex">
                <div>原图</div>
                <div>背景图</div>
                <Textarea />
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </form>
    </Dialog>
  );
}
